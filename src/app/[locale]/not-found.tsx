'use client';

import { useTranslations } from 'next-intl';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import {
  Home,
  Search,
  Gamepad2,
  Zap,
  RotateCcw,
  ArrowLeft,
  Lightbulb,
  Trophy,
  Star,
  Sparkles,
  Target,
  Rocket
} from 'lucide-react';
import { useState, useEffect } from 'react';

export default function NotFound() {
  const t = useTranslations('NotFound');
  const params = useParams();
  const locale = params?.locale as string || 'en';
  
  // Random fun fact selection
  const [currentFact, setCurrentFact] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');
  const facts = t.raw('funFacts.facts') as string[];
  
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentFact((prev) => (prev + 1) % facts.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [facts.length]);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      switch (event.key) {
        case 'Escape':
          window.history.back();
          break;
        case 'Enter':
          window.location.href = `/${locale}`;
          break;
        case 'h':
        case 'H':
          if (event.ctrlKey || event.metaKey) {
            event.preventDefault();
            window.location.href = `/${locale}`;
          }
          break;
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [locale]);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 100
      }
    }
  };

  const floatingVariants = {
    animate: {
      y: [-10, 10, -10],
      transition: {
        duration: 3,
        repeat: Infinity,
        ease: "easeInOut"
      }
    }
  };

  const glowVariants = {
    animate: {
      boxShadow: [
        "0 0 20px rgba(244, 81, 30, 0.3)",
        "0 0 40px rgba(244, 81, 30, 0.5)",
        "0 0 20px rgba(244, 81, 30, 0.3)"
      ],
      transition: {
        duration: 2,
        repeat: Infinity,
        ease: "easeInOut"
      }
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-secondary/20 flex items-center justify-center p-4">
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="max-w-4xl w-full space-y-8"
      >
        {/* Main 404 Display */}
        <motion.div 
          variants={itemVariants}
          className="text-center space-y-6"
        >
          {/* Animated 404 with Game Controller */}
          <div className="relative">
            <motion.div
              variants={floatingVariants}
              animate="animate"
              className="inline-flex items-center justify-center"
            >
              <motion.div
                variants={glowVariants}
                animate="animate"
                className="relative"
              >
                <span className="text-8xl md:text-9xl font-bold text-primary bg-gradient-to-r from-primary to-orange-500 bg-clip-text text-transparent">
                  4
                </span>
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 4, repeat: Infinity, ease: "linear" }}
                  className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
                >
                  <Gamepad2 className="h-16 w-16 md:h-20 md:w-20 text-primary" />
                </motion.div>
                <span className="text-8xl md:text-9xl font-bold text-primary bg-gradient-to-r from-orange-500 to-primary bg-clip-text text-transparent">
                  4
                </span>
              </motion.div>
            </motion.div>
          </div>

          {/* Title and Description */}
          <div className="space-y-4">
            <motion.h1 
              variants={itemVariants}
              className="text-4xl md:text-5xl font-bold text-foreground"
            >
              {t('title')}
            </motion.h1>
            
            <motion.div variants={itemVariants}>
              <Badge variant="destructive" className="text-lg px-4 py-2 mb-4">
                {t('subtitle')}
              </Badge>
            </motion.div>
            
            <motion.p 
              variants={itemVariants}
              className="text-lg md:text-xl text-muted-foreground max-w-2xl mx-auto leading-relaxed"
            >
              {t('description')}
            </motion.p>
            
            <motion.p 
              variants={itemVariants}
              className="text-base text-muted-foreground italic"
            >
              {t('gameOverMessage')}
            </motion.p>
          </div>
        </motion.div>

        {/* Action Buttons */}
        <motion.div 
          variants={itemVariants}
          className="flex flex-col sm:flex-row gap-4 justify-center items-center"
        >
          <Button asChild size="lg" className="hover-glow group">
            <Link href={`/${locale}`}>
              <Home className="h-5 w-5 mr-2 group-hover:scale-110 transition-transform" />
              {t('actions.backHome')}
            </Link>
          </Button>
          
          <Button asChild variant="outline" size="lg" className="hover-glow group">
            <Link href={`/${locale}/games`}>
              <Gamepad2 className="h-5 w-5 mr-2 group-hover:scale-110 transition-transform" />
              {t('actions.browseGames')}
            </Link>
          </Button>
          
          <Button variant="ghost" size="lg" className="group" onClick={() => window.history.back()}>
            <ArrowLeft className="h-5 w-5 mr-2 group-hover:scale-110 transition-transform" />
            Go Back
          </Button>
        </motion.div>

        {/* Quick Search */}
        <motion.div variants={itemVariants} className="max-w-md mx-auto">
          <Card className="glass-card">
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Search className="h-5 w-5 text-muted-foreground" />
                <input
                  type="text"
                  placeholder={t('actions.searchGames')}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyPress={(e) => {
                    if (e.key === 'Enter' && searchQuery.trim()) {
                      window.location.href = `/${locale}?search=${encodeURIComponent(searchQuery)}`;
                    }
                  }}
                  className="flex-1 bg-transparent border-none outline-none text-sm placeholder:text-muted-foreground"
                />
                {searchQuery && (
                  <Button
                    size="sm"
                    onClick={() => {
                      if (searchQuery.trim()) {
                        window.location.href = `/${locale}?search=${encodeURIComponent(searchQuery)}`;
                      }
                    }}
                  >
                    Search
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Suggestions and Fun Fact Cards */}
        <div className="grid md:grid-cols-2 gap-6">
          {/* Suggestions Card */}
          <motion.div variants={itemVariants}>
            <Card className="glass-card hover-glow">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Lightbulb className="h-5 w-5 text-primary" />
                  {t('suggestions.title')}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-3">
                  {(t.raw('suggestions.items') as string[]).map((item, index) => (
                    <motion.li
                      key={index}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.5 + index * 0.1 }}
                      className="flex items-start gap-2 text-sm text-muted-foreground"
                    >
                      <Zap className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                      {item}
                    </motion.li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          </motion.div>

          {/* Fun Fact Card */}
          <motion.div variants={itemVariants}>
            <Card className="glass-card hover-glow">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Trophy className="h-5 w-5 text-primary" />
                  {t('funFacts.title')}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <motion.div
                  key={currentFact}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.5 }}
                  className="space-y-3"
                >
                  <div className="flex items-center gap-2 mb-2">
                    <Star className="h-4 w-4 text-yellow-500" />
                    <Badge variant="secondary">Fact #{currentFact + 1}</Badge>
                  </div>
                  <p className="text-sm text-muted-foreground leading-relaxed">
                    {facts[currentFact]}
                  </p>
                </motion.div>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Decorative Elements */}
        <motion.div
          variants={itemVariants}
          className="flex justify-center items-center space-x-4 opacity-50"
        >
          {[Gamepad2, Target, Rocket, Sparkles, Trophy].map((Icon, i) => (
            <motion.div
              key={i}
              animate={{
                scale: [1, 1.2, 1],
                opacity: [0.3, 0.7, 0.3],
                rotate: [0, 360]
              }}
              transition={{
                duration: 3,
                repeat: Infinity,
                delay: i * 0.4,
                ease: "easeInOut"
              }}
            >
              <Icon className="h-6 w-6 text-primary" />
            </motion.div>
          ))}
        </motion.div>

        {/* Additional Gaming Elements */}
        <motion.div
          variants={itemVariants}
          className="text-center space-y-4"
        >
          <div className="flex justify-center items-center gap-2 text-sm text-muted-foreground">
            <span>Press</span>
            <kbd className="px-2 py-1 bg-secondary rounded text-xs font-mono">ESC</kbd>
            <span>to go back or</span>
            <kbd className="px-2 py-1 bg-secondary rounded text-xs font-mono">ENTER</kbd>
            <span>to continue</span>
          </div>

          <div className="flex justify-center items-center gap-4 text-xs text-muted-foreground">
            <div className="flex items-center gap-1">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span>Server Online</span>
            </div>
            <div className="flex items-center gap-1">
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
              <span>Games Available</span>
            </div>
            <div className="flex items-center gap-1">
              <div className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse"></div>
              <span>Players Active</span>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </div>
  );
}
